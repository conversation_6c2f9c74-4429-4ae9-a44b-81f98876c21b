# 权利要求书模板

## 撰写原则

1. 清晰、精确、无歧义
2. 使用"所述"指代前文实体
3. 独立权利要求包含必要技术特征，从属权利要求包含具体实现细节
4. **直接描述技术方案**，避免"对...进行..."等描述性标题
5. **算法步骤直接陈述**，以条件或动作开始，如"当...时"、"将..."

---

## 权利要求书

### 独立权利要求

^^CONDITION: claim_type == "system"^^
**1. 一种{{invention_name}}，其特征在于，包括：**

<!-- 系统类权利要求：使用"所述[组件名称]用于[功能描述]"句式 -->

{{component_1}}，用于{{function_1}}；

{{component_2}}，用于{{function_2}}；

{{component_3}}，用于{{function_3}}；

其中，{{additional_technical_features}}。

^^/CONDITION: claim_type^^

^^CONDITION: claim_type == "method"^^
**1. 一种{{method_name}}，其特征在于，按如下步骤进行：**

<!-- 方法类权利要求：步骤以强动词开头，使用"从而..."连接因果关系，"若...则..."表达条件逻辑 -->

步骤1、{{step_1_action}}{{step_1_input}}，{{step_1_operation}}，{{step_1_output}}；

步骤2、{{step_2_action}}{{step_2_input}}，利用{{method_or_formula}}{{step_2_operation}}，从而{{step_2_result}}；

步骤3、将{{data_input}}与{{threshold_or_standard}}进行比较，判断{{condition}}；若{{condition_met}}，则{{action_if_true}}；

<<REPEAT section="additional_step" count="{{additional_steps_count}}">>
步骤{{step_number}}、{{step_action}}{{step_details}}；
<</REPEAT>>

其中，{{technical_parameters_or_formulas}}。

^^/CONDITION: claim_type^^

### 从属权利要求

<!-- 从属权利要求：使用"根据权利要求X所述的..."开头，进一步限定主权利要求 -->

**2. 根据权利要求1所述的{{invention_type}}，其特征在于，所述{{component_or_step_name}}包括：**

{{detailed_feature_1}}；

{{detailed_feature_2}}；

{{detailed_feature_3}}。

**3. 根据权利要求1所述的{{invention_type}}，其特征在于，所述{{specific_component}}的组成包括：**

{{sub_component_1}}，用于{{sub_function_1}}；

{{sub_component_2}}，用于{{sub_function_2}}；

其中，{{technical_specifications}}。

<<REPEAT section="dependent_claim" count="{{dependent_claims_count}}">>
**{{claim_number}}. 根据权利要求{{reference_claim}}所述的{{invention_type}}，其特征在于，{{additional_limitation}}。**
<</REPEAT>>

### 电子设备权利要求

^^CONDITION: includes_electronic_device == "yes"^^
**{{device_claim_number}}. 一种电子设备，包括存储器以及处理器，其特征在于，所述存储器用于存储支持处理器执行权利要求{{method_claim_range}}中任一所述{{method_name}}的程序，所述处理器被配置为用于执行所述存储器中存储的程序。**

**{{storage_claim_number}}. 一种计算机可读存储介质，计算机可读存储介质上存储有计算机程序，其特征在于，所述计算机程序被处理器运行时执行权利要求{{method_claim_range}}中任一所述{{method_name}}的步骤。**
^^/CONDITION: includes_electronic_device^^

---

## 撰写检查要点

### 语言规范检查
1. 正确使用"所述"指代前文实体
2. 功能描述使用标准"用于..."句式
3. 方法步骤以强动词开头
4. 避免模糊、宽泛的措辞

### 逻辑结构检查
1. 独立权利要求包含必要技术特征
2. 从属权利要求合理限定主权利要求
3. 权利要求引用关系正确
4. 技术方案具有可实施性

### 保护范围检查
1. 保护范围合理
2. 体现发明的技术贡献
3. 避免不必要的限制

---

## 常用句式参考

### 系统类功能描述
- "所述[组件名称]用于[功能描述]"
- "所述[组件名称]包括[子组件列表]"
- "所述[组件名称]设置在[位置]，用于[功能]"

### 方法类步骤描述
- "步骤X、[动词][操作对象]，[具体操作]，[结果]"
- "利用[方法/公式][操作]，从而[结果]"
- "将[数据]与[标准]进行比较，判断[条件]；若[条件]，则[动作]"

### 条件和结果连接
- "从而获得/计算/实现..."
- "若...则..."
- "其中，..."

<!-- 完成后检查：使用专利质量检查清单验证，确保与说明书内容一致 -->
