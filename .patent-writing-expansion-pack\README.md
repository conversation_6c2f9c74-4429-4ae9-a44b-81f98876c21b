# 专利撰写扩展包

## 概述

专业的专利申请文件撰写和质量控制系统，基于实际专利案例和用户自定义撰写规范创建。

## 🎯 核心代理团队

### 李明华 - 专利撰写项目协调员
*资深专利代理师，15年专业经验*

李明华是您的专利撰写项目协调员，将引导您完成整个专利申请文件撰写流程。

**启动命令**: `@patent-writing-orchestrator`

### 专家团队
- **张技术** - 专利分析专家 (`@patent-analyst`)
- **王文档** - 专利撰写专家 (`@patent-writer`)
- **陈审查** - 专利质量审查员 (`@patent-reviewer`)
- **严教授** - 严格批判审查专家 (`@patent-critic`)

## 🚀 快速开始

### 1. 准备数据文件
将以下文件放置在 `bmad-core/data/` 目录：

- `patent-writing-guidelines.md` - 专利撰写规范
- `technical-terminology.md` - 技术术语库
- `patent-examples.md` - 参考案例

### 2. 启动协调员
```bash
@patent-writing-orchestrator
```

### 3. 选择服务
李明华将提供编号选项，输入数字选择相应服务。

## 📋 主要功能

### 文档创建
- ✅ 完整专利申请书撰写
- ✅ 权利要求书起草
- ✅ 说明书编写
- ✅ 摘要撰写

### 质量控制
- ✅ 三级质量检查（基础/综合/专家）
- ✅ 星级评分系统
- ✅ 格式规范验证
- ✅ 法律合规性审查

### 流程管理
- ✅ 专利撰写工作流指导
- ✅ 任务分配和进度跟踪
- ✅ 团队协作支持

## 🎨 核心特性

### 智能模板系统
- **LLM指令嵌入**: 逐步引导撰写
- **条件内容**: 根据专利类型自动调整
- **动态变量**: 智能内容替换

### 专业撰写规范
严格遵循专利撰写标准：
- 正确使用"所述"指代
- 标准"用于..."功能描述句式
- 方法步骤强动词开头
- "从而..."和"若...则..."逻辑连接

### 质量保证体系
- 三级验证系统
- 星级评分标准
- 具体改进建议

### 用户确认机制
- **修改前确认**：任何修改专利文件的操作都必须先获得用户明确同意
- **详细说明**：修改前会详细说明修改位置、内容和原因
- **用户控制**：用户完全控制是否执行修改建议

### 精简写作控制
- **字数限制**：严格控制回复字数，避免冗余解释
- **直接表达**：直接描述技术方案和问题，不做无关说明
- **高效沟通**：在有限字数内准确传达核心信息

### 修改标记机制
- **保留原文**：修改时不删除原始内容
- **标记修改**：新内容写在原文下方，用"**修改提示**"标记
- **清晰对比**：用户可清楚看到原文和修改建议的对比

## 📚 组件结构

```
patent-writing-expansion-pack/
├── agents/                    # AI代理定义
│   ├── patent-writing-orchestrator.md
│   ├── patent-analyst.md
│   ├── patent-writer.md
│   ├── patent-reviewer.md
│   └── patent-critic.md
├── tasks/                     # 任务定义
│   ├── create-doc.md
│   ├── analyze-technical-disclosure.md
│   ├── draft-claims.md
│   ├── write-specification.md
│   └── review-patent-quality.md
├── templates/                 # 文档模板
│   ├── patent-application-tmpl.md
│   ├── claims-tmpl.md
│   ├── specification-tmpl.md
│   └── abstract-tmpl.md
├── checklists/               # 质量检查清单
│   ├── patent-quality-checklist.md
│   ├── claims-quality-checklist.md
│   └── format-compliance-checklist.md
├── data/                     # 领域知识
│   ├── patent-law-basics.md
│   ├── patent-writing-standards.md
│   └── technical-writing-principles.md
├── utils/                    # 工具文件
│   ├── template-format.md
│   └── workflow-management.md
├── workflows/                # 工作流定义
│   └── patent-writing-workflow.md
└── agent-teams/             # 团队配置
    └── patent-team.yml
```

## 🔧 使用示例

### 创建权利要求书
```
1. @patent-writing-orchestrator
2. 选择: *create-doc claims-tmpl
3. 按照智能提示完成撰写
4. 使用质量检查清单验证
```

### 完整专利申请
```
1. @patent-writing-orchestrator
2. 选择: *create-doc patent-application-tmpl
3. 遵循工作流程指导
4. 进行多级质量检查
```

## 📖 撰写规范要点

### 系统类权利要求
- 使用"所述[组件名称]用于[功能描述]"句式
- 确保组件功能描述清晰、具体
- 明确组件间的连接关系

### 方法类权利要求
- 步骤以强动词开头
- 清晰描述输入-操作-输出关系
- 使用"从而..."连接因果关系
- 使用"若...则..."表达条件逻辑

## 🎯 质量标准

- ⭐⭐⭐⭐⭐ 优秀 - 可直接提交申请
- ⭐⭐⭐⭐ 良好 - 经小幅修改后可提交
- ⭐⭐⭐ 合格 - 基本满足申请要求
- ⭐⭐ 需改进 - 存在较多问题
- ⭐ 不合格 - 需要大幅修改

## 🔄 工作流程

1. **项目启动** → 技术交底书分析
2. **技术分析** → 现有技术检索和专利性评估
3. **文档撰写** → 权利要求书和说明书起草
4. **质量审查** → 多级质量检查和格式验证
5. **用户确认** → 所有修改建议都需要用户明确同意
6. **最终交付** → 完成的专利申请文件

## 🔒 用户确认机制

### 修改确认流程
1. **详细说明**：明确说明要修改的位置、内容和原因
2. **等待确认**：等待用户明确回复"同意"、"确认"或"是"
3. **执行修改**：仅在获得用户同意后才执行修改
4. **确认结果**：修改完成后向用户展示结果

### 适用范围
- 修改权利要求书内容
- 修改说明书内容
- 修改摘要内容
- 调整技术方案描述
- 更改技术术语或表达方式
- 删除或添加技术特征

## 📞 支持

- 使用 `*help` 查看可用命令
- 参考质量检查清单进行自检
- 遵循李明华的专业指导

---

**注意**: 本扩展包基于实际专利案例和用户撰写规范创建，确保输出符合专业标准。
